
  "document_annotation_format": {

        "type": "json_schema",

        "json_schema": {

            "schema": {

                "properties": {

                    "Vendor Name": {"title": "Vendor Name", "type": "string"},

                    "Account/Customer Number": {"title": "Account/Customer Number", "type": "string"},
                    
                    "Invoice Number": {"title": "Invoice Number", "type": "string"},
                    
                    "Bill/Invoice Date": {"title": "Bill/Invoice Date", "type": "string"},
                    
                    "Current Charges": {"title": "Current Charges", "type": "string"},

                    "Previous Charges": {"title": "Previous Charges", "type": "string"},

                    "Balance Forward": {"title": "Balance Forward", "type": "string"},

                    "Adjustments": {"title": "Adjustments", "type": "string"},

                    "Payment": {"title": "Payment", "type": "string"},
                    
                    "Late Pay": {"title": "Late Pay", "type": "string"},

                    "Credits": {"title": "Credits", "type": "string"},

                    "Tax total (USD)": {"title": "Tax Total (USD)", "type": "string"},
                
                    "Finance Charges": {"title": "Finance Charges", "type": "string"},

                    "Service total (USD)": {"title": "Service Total (USD)", "type": "string"},

                    "Usage": {"title": "Usage", "type": "string"},

                    "Location Associated with charges" : {"title": "Location Associated", "type": "string"},


                      "Services_detail": {"title": "Services_detail", "type": "object", 
                              
                        "description": "An object for service details. Define specific properties here if needed.(Will never be a location or address) ",

                        "additionalProperties": false
                                          }

                },

                "required": ["Customer Number", "Invoice Number", "Vendor Name"],

                "title": "DocumentAnnotation",

                "type": "object",

                "additionalProperties": false

            },

            "name": "document_annotation",

            "strict": true

        }

    }




// Output:
{
  "Vendor Name": "GTT Americas LLC",
  "Account/Customer Number": "S5000441",
  "Invoice Number": "INV10536224",
  "Bill/Invoice Date": "01-May-25",
  "Current Charges": "$3,184.56",
  "Previous Charges": "$3,384.97",
  "Balance Forward": "",
  "Adjustments": "",
  "Payment": "",
  "Late Pay": "",
  "Credits": "",
  "Tax total (USD)": "$25.36",
  "Finance Charges": "$50.77",
  "Service total (USD)": "$3,235.33",
  "Usage": "",
  "Location Associated with charges": "Jeff Lartigue - 6902 E Greenway Pkwy, Scottsdale AZ 85254-8123 USA",
  "Services_detail": {
    "Ethernet - 2000M": {
      "Amount": "$2,425.00",
      "Start Date": "01-May-25",
      "End Date": "31-May-25"
    },
    "US Broadband Wireless Unlimited - 5G Primary - Medium 100/20": {
      "Amount": "$92.00",
      "Start Date": "01-May-25",
      "End Date": "31-May-25"
    },
    "Cradlepoint W1850 (US/CAN) Series 5G/4G Wideband Adapter": {
      "Amount": "$195.00",
      "Start Date": "01-May-25",
      "End Date": "31-May-25"
    },
    "Amortized Professional Installation USA/Canada": {
      "Amount": "$44.00",
      "Start Date": "01-May-25",
      "End Date": "31-May-25"
    },
    "Amortized Shipping and Handling USA/Canada": {
      "Amount": "$5.00",
      "Start Date": "01-May-25",
      "End Date": "31-May-25"
    },
    "Cost Recovery Surcharge": {
      "Amount": "$398.27",
      "Type": "Fee"
    },
    "Rental Tax": {
      "Amount": "$19.72",
      "Type": "Tax"
    },
    "Telecommunications Sales Tax": {
      "Amount": "$5.57",
      "Type": "Tax"
    }
  }
}