import asyncio
from fastmcp import Client, TextContent # Import TextContent for type checking
import json # To pretty-print the JSON response

# The MCP server will be running on localhost:8001 based on docker-compose.yml
MCP_SERVER_ENDPOINT = "http://localhost:8001/mcp"

async def call_example_invoice_tool():
    print(f"Attempting to connect to MCP server at: {MCP_SERVER_ENDPOINT}")
    try:
        async with C<PERSON>(MCP_SERVER_ENDPOINT) as client:
            # Ping the server to confirm connection (optional, but good practice)
            is_connected = await client.ping()
            print(f"Successfully connected to MCP server: {is_connected}")

            # Test Case 1: Vendor exists
            vendor_to_query = "AcmeCorp"
            print(f"\nCalling tool 'ExampleInvoiceJsons' for vendor: '{vendor_to_query}'")
            # call_tool returns a list of content blocks
            response_blocks = await client.call_tool(
                "ExampleInvoiceJsons",
                {"vendor_name": vendor_to_query}
            )
            
            # Check if we got a response and the first block is TextContent
            if response_blocks and isinstance(response_blocks[0], TextContent):
                text_block = response_blocks[0]
                if text_block.text:
                    try:
                        invoices = json.loads(text_block.text)
                        print(f"Received {len(invoices)} invoice(s) for '{vendor_to_query}':")
                        for i, invoice in enumerate(invoices):
                            print(f"--- Invoice {i+1} ---")
                            print(json.dumps(invoice, indent=2))
                    except json.JSONDecodeError:
                        print(f"Error: Could not parse JSON response: {text_block.text}")
                else:
                    print(f"Received TextContent block but it has no text for '{vendor_to_query}'.")
            else:
                print(f"No valid TextContent block received for '{vendor_to_query}'. Response: {response_blocks}")

            # Test Case 2: Vendor does not exist
            vendor_not_exists = "NonExistentVendor"
            print(f"\nCalling tool 'ExampleInvoiceJsons' for vendor: '{vendor_not_exists}'")
            response_blocks_nonexistent = await client.call_tool(
                "ExampleInvoiceJsons",
                {"vendor_name": vendor_not_exists}
            )
            
            if response_blocks_nonexistent and isinstance(response_blocks_nonexistent[0], TextContent):
                text_block_nonexistent = response_blocks_nonexistent[0]
                if text_block_nonexistent.text:
                    try:
                        invoices_nonexistent = json.loads(text_block_nonexistent.text)
                        # The server's ExampleInvoiceJsons tool returns an empty list [] for non-existent vendors.
                        # json.loads('[]') results in an empty Python list.
                        if isinstance(invoices_nonexistent, list) and not invoices_nonexistent:
                            print(f"Received an empty list for '{vendor_not_exists}', as expected.")
                        else:
                            print(f"Unexpected response for '{vendor_not_exists}': {invoices_nonexistent}")
                    except json.JSONDecodeError:
                        print(f"Error: Could not parse JSON response for non-existent vendor: {text_block_nonexistent.text}")
                else:
                    print(f"Received TextContent block but it has no text for '{vendor_not_exists}'.")
            elif not response_blocks_nonexistent: # If server returns completely empty response
                 print(f"Received no response blocks for '{vendor_not_exists}', possibly indicating empty list.")
            else:
                print(f"No valid TextContent block received for '{vendor_not_exists}'. Response: {response_blocks_nonexistent}")

    except ConnectionRefusedError:
        print(f"Connection refused. Ensure the FastMCP server is running at {MCP_SERVER_ENDPOINT} and port 8001 is mapped.")
    except Exception as e:
        print(f"An error occurred: {e}")

if __name__ == "__main__":
    # Ensure fastmcp is installed in the environment where you run this client
    # pip install fastmcp
    asyncio.run(call_example_invoice_tool())