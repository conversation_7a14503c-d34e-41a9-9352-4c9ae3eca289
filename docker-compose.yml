# This is a comment, lines starting with # are ignored
# Ensure there are NO spaces before 'version:'

# Ensure there are NO spaces before 'services:'
services:

  # This line MUST have exactly 2 spaces at the beginning
  mcp_server:

    # This line MUST have exactly 4 spaces at the beginning
    build:

      # This line MUST have exactly 6 spaces at the beginning
      context: ./

      # This line MUST have exactly 6 spaces at the beginning
      dockerfile: Dockerfile

    # This line MUST have exactly 4 spaces at the beginning
    ports:

      # This line MUST have exactly 6 spaces at the beginning, then a dash, then a space
      - "8001:8001"

    # This line (optional) MUST have exactly 4 spaces at the beginning
    container_name: my_mcp_server_instance

    # This line (optional) MUST have exactly 4 spaces at the beginning
    restart: unless-stopped