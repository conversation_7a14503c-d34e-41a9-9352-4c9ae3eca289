# Use an official Python runtime as a parent image
FROM python:3.10-slim

# Set the working directory in the container
WORKDIR /app

# Copy the requirements file into the container at /app
COPY requirements.txt .

# Install any needed packages specified in requirements.txt
RUN pip install --no-cache-dir -r requirements.txt

# Copy the application script and data file into the container at /app
COPY mcp_server.py .
# COPY invoice_data.json .  # Commented out: Uncomment if/when invoice_data.json is created and needed

# Make port 8001 available to the world outside this container
# This is documentation; actual port mapping is done in docker-compose.yml or `docker run -p`
EXPOSE 8001

# Define environment variable (optional, can be overridden)
ENV MCP_SERVER_PORT 8001

# Run mcp_server.py when the container launches
# Uvicorn will serve the 'http_app' ASGI application from 'mcp_server.py'
CMD ["uvicorn", "mcp_server:http_app", "--host", "0.0.0.0", "--port", "8001"]
