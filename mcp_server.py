from fastmcp import FastMCP
from typing import List, Dict, Any, Optional

# Instantiate the FastMCP server
mcp = FastMCP(
    name="InvoiceExampleServer",
    description="An MCP server that provides example invoice JSONs by vendor."
)

# Sample data: A dictionary where keys are vendor names and values are lists of invoice JSONs (as Python dicts)
# In a real application, this data might come from a database or a file.
SAMPLE_INVOICE_DATA: Dict[str, List[Dict[str, Any]]] = {
    "AcmeCorp": [
        {
            "invoice_id": "INV-ACME-001",
            "vendor_name": "AcmeCorp",
            "client_name": "Beta Solutions",
            "date": "2024-05-01",
            "due_date": "2024-06-01",
            "total_amount": 1200.50,
            "currency": "USD",
            "items": [
                {"description": "Product A", "quantity": 2, "unit_price": 300.25, "line_total": 600.50},
                {"description": "Service B", "quantity": 1, "unit_price": 600.00, "line_total": 600.00}
            ]
        },
        {
            "invoice_id": "INV-ACME-002",
            "vendor_name": "AcmeCorp",
            "client_name": "Gamma Innovations",
            "date": "2024-05-10",
            "due_date": "2024-06-10",
            "total_amount": 750.00,
            "currency": "USD",
            "items": [
                {"description": "Consulting Hours", "quantity": 10, "unit_price": 75.00, "line_total": 750.00}
            ]
        }
    ],
    "GlobexInc": [
        {
            "invoice_id": "INV-GLOBEX-001",
            "vendor_name": "GlobexInc",
            "client_name": "Delta Corp",
            "date": "2024-04-15",
            "due_date": "2024-05-15",
            "total_amount": 2500.00,
            "currency": "USD",
            "items": [
                {"description": "Software License", "quantity": 1, "unit_price": 2000.00, "line_total": 2000.00},
                {"description": "Support Package", "quantity": 1, "unit_price": 500.00, "line_total": 500.00}
            ]
        }
    ],
    "StarkIndustries": [
        {
            "invoice_id": "INV-STARK-001",
            "vendor_name": "StarkIndustries",
            "client_name": "Wayne Enterprises",
            "date": "2024-03-20",
            "due_date": "2024-04-20",
            "total_amount": 15000.75,
            "currency": "USD",
            "items": [
                {"description": "Advanced Component X", "quantity": 5, "unit_price": 2500.00, "line_total": 12500.00},
                {"description": "Installation Services", "quantity": 1, "unit_price": 2500.75, "line_total": 2500.75}
            ]
        }
    ]
}

@mcp.tool()
def ExampleInvoiceJsons(vendor_name: str) -> List[Dict[str, Any]]:
    """
    Retrieves a list of example invoice JSONs for the specified vendor.

    Args:
        vendor_name: The name of the vendor to fetch example invoices for.
                     Case-sensitive (e.g., "AcmeCorp", "GlobexInc").

    Returns:
        A list of invoice JSON objects (as Python dictionaries) for the given vendor.
        Returns an empty list if the vendor is not found.
    """
    # Perform a case-sensitive lookup for the vendor name
    matching_invoices: Optional[List[Dict[str, Any]]] = SAMPLE_INVOICE_DATA.get(vendor_name)
    
    if matching_invoices is None:
        return [] # Return an empty list if vendor not found, as per docstring
    
    return matching_invoices

# Expose the FastMCP server as an ASGI application.
# This 'http_app' will be served by Uvicorn.
# The MCP endpoints will be available under the "/mcp" path.
http_app = mcp.http_app(path="/mcp")

# The "Import "fastmcp" could not be resolved" Pylance error is likely an issue
# with your local Python environment's linter setup. If fastmcp is in your
# requirements.txt and installed in the Docker container, this error won't
# affect the container's execution. Ensure your local environment (e.g., virtualenv)
# has fastmcp installed if you want Pylance to resolve it locally.

# If you were to run this file directly (e.g., python mcp_server.py),
# you could add the following, but it's better to use Uvicorn directly for Docker.
# if __name__ == "__main__":
#     import uvicorn
#     # This mcp.run() is a convenience wrapper that uses uvicorn.run() for HTTP transports.
#     # mcp.run(transport="streamable-http", host="0.0.0.0", port=8001, path="/mcp")
#     # Or, using uvicorn directly with the http_app:
#     uvicorn.run(http_app, host="0.0.0.0", port=8001)
