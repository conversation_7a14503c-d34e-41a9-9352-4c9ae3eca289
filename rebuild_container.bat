@echo off
REM PyMuPDF Flask API - Container Rebuild Script (Windows)
REM This script removes existing containers and images, then rebuilds and runs the application

setlocal

REM Configuration
set CONTAINER_NAME=PyMuPDF-container
set IMAGE_NAME=pymupdf-api
set HOST_PORT=5001
set CONTAINER_PORT=5000

echo 🚀 Starting PyMuPDF Flask API container rebuild...
echo ================================================

REM Check if Docker is running
docker info >nul 2>&1
if errorlevel 1 (
    echo ❌ Error: Docker is not running. Please start Docker and try again.
    exit /b 1
)

REM Stop and remove existing container
echo 🔍 Checking for existing container: %CONTAINER_NAME%
docker ps -a --format "table {{.Names}}" | findstr /R "^%CONTAINER_NAME%$" >nul 2>&1
if not errorlevel 1 (
    echo 🛑 Stopping container: %CONTAINER_NAME%
    docker stop %CONTAINER_NAME% >nul 2>&1
    
    echo 🗑️  Removing container: %CONTAINER_NAME%
    docker rm %CONTAINER_NAME%
    echo    ✅ Container removed successfully
) else (
    echo    ℹ️  No existing container found
)

REM Remove existing image
echo 🔍 Checking for existing image: %IMAGE_NAME%
docker images --format "table {{.Repository}}" | findstr /R "^%IMAGE_NAME%$" >nul 2>&1
if not errorlevel 1 (
    echo 🗑️  Removing image: %IMAGE_NAME%
    docker rmi %IMAGE_NAME%
    echo    ✅ Image removed successfully
) else (
    echo    ℹ️  No existing image found
)

REM Clean up dangling images
echo 🧹 Cleaning up dangling images...
for /f %%i in ('docker images -f "dangling=true" -q 2^>nul') do (
    docker rmi %%i >nul 2>&1
)
echo    ✅ Dangling images cleaned up

echo.

REM Build new image
echo 🏗️  Building new Docker image: %IMAGE_NAME%
docker build -t %IMAGE_NAME% .
if errorlevel 1 (
    echo ❌ Error building image
    exit /b 1
)
echo    ✅ Image built successfully

echo.

REM Run new container
echo 🚢 Running new container: %CONTAINER_NAME%
docker run -d -p %HOST_PORT%:%CONTAINER_PORT% --name %CONTAINER_NAME% %IMAGE_NAME%
if errorlevel 1 (
    echo ❌ Error starting container
    exit /b 1
)
echo    ✅ Container started successfully
echo    🌐 Application available at: http://localhost:%HOST_PORT%

echo.

REM Show status
echo 📊 Container Status:
echo ===================
docker ps --filter "name=%CONTAINER_NAME%" --format "table {{.Names}}\t{{.Status}}\t{{.Ports}}"

echo.
echo 📊 Image Info:
echo ==============
docker images --filter "reference=%IMAGE_NAME%" --format "table {{.Repository}}\t{{.Tag}}\t{{.Size}}\t{{.CreatedAt}}"

echo.
echo 🎉 Rebuild complete! Your PyMuPDF Flask API is ready.
echo 📝 To view logs: docker logs %CONTAINER_NAME%
echo 🛑 To stop: docker stop %CONTAINER_NAME%

endlocal 